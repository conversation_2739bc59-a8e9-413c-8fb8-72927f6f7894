package ptype.def.builder;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import ptype.def.typedef.*;
import ptype.types.*;

public class PDefArrayBuilderTest {

    @Test
    @DisplayName("Should create basic array with single allowed type")
    void testBasicArrayCreation() {
        PDefArrayBuilder builder = new PDefArrayBuilder();
        PDefArray arrayDef = builder
            .addAllowedType(PDefString.DefaultDef)
            .Build();
        
        assertNotNull(arrayDef);
        
        // Create an instance to verify it works
        PTArray array = arrayDef.createNewType();
        assertNotNull(array);
    }

    @Test
    @DisplayName("Should create array with multiple allowed types")
    void testMultipleAllowedTypes() {
        PDefArrayBuilder builder = new PDefArrayBuilder();
        PDefArray arrayDef = builder
            .addAllowedType(PDefString.DefaultDef)
            .addAllowedType(PDefInteger.DefaultDef)
            .addAllowedType(PDefBoolean.DefaultDef)
            .Build();
        
        assertNotNull(arrayDef);
        PTArray array = arrayDef.createNewType();
        assertNotNull(array);
    }

    @Test
    @DisplayName("Should create open array with size constraints")
    void testOpenArrayWithConstraints() {
        PDefArrayBuilder builder = new PDefArrayBuilder();
        PDefArray arrayDef = builder
            .setOpenArray(true)
            .setMinElements(2)
            .setMaxElements(10)
            .addAllowedType(PDefString.DefaultDef)
            .addAllowedType(PDefInteger.DefaultDef)
            .Build();
        
        assertNotNull(arrayDef);
        PTArray array = arrayDef.createNewType();
        assertNotNull(array);
    }

    @Test
    @DisplayName("Should support method chaining")
    void testMethodChaining() {
        PDefArray arrayDef = new PDefArrayBuilder()
            .setOpenArray(false)
            .setMinElements(1)
            .setMaxElements(5)
            .addAllowedType(PDefString.DefaultDef)
            .addAllowedType(PDefFloat.DefaultDef)
            .Build();
        
        assertNotNull(arrayDef);
    }

    @Test
    @DisplayName("Should clear and set allowed types")
    void testClearAndSetAllowedTypes() {
        PDefArrayBuilder builder = new PDefArrayBuilder();
        
        // Add some types first
        builder.addAllowedType(PDefString.DefaultDef)
               .addAllowedType(PDefInteger.DefaultDef);
        
        // Clear and set new types
        PDefArray arrayDef = builder
            .clearAllowedTypes()
            .addAllowedType(PDefBoolean.DefaultDef)
            .addAllowedType(PDefDouble.DefaultDef)
            .Build();
        
        assertNotNull(arrayDef);
    }

    @Test
    @DisplayName("Should set allowed types using varargs")
    void testSetAllowedTypesVarargs() {
        PDefArrayBuilder builder = new PDefArrayBuilder();
        PDefArray arrayDef = builder
            .setAllowedTypes(PDefString.DefaultDef, PDefInteger.DefaultDef, PDefBoolean.DefaultDef)
            .Build();
        
        assertNotNull(arrayDef);
    }

    @Test
    @DisplayName("Should work with custom integer type from builder")
    void testWithCustomIntegerType() {
        // Create a custom integer type using its builder
        PDefIntegerBuilder intBuilder = new PDefIntegerBuilder();
        PDefInteger customInt = intBuilder.addRange(-100, 100).Build();
        
        // Use it in array builder
        PDefArrayBuilder arrayBuilder = new PDefArrayBuilder();
        PDefArray arrayDef = arrayBuilder
            .addAllowedType(customInt)
            .addAllowedType(PDefString.DefaultDef)
            .setMinElements(0)
            .setMaxElements(20)
            .Build();
        
        assertNotNull(arrayDef);
        PTArray array = arrayDef.createNewType();
        assertNotNull(array);
    }

    @Test
    @DisplayName("Should create array with default values")
    void testDefaultValues() {
        PDefArrayBuilder builder = new PDefArrayBuilder();
        PDefArray arrayDef = builder
            .addAllowedType(PDefString.DefaultDef)
            .Build();
        
        assertNotNull(arrayDef);
        // The array should be created with default values:
        // - openArray = false
        // - minElements = MIN_UNSPECIFIED_MIN_ELEMENTS
        // - maxElements = MAX_UNSPECIFIED_MAX_ELEMENTS
    }
}
