package ptype;

import patterns.comparable.IComparable;
import patterns.copyObj.ICopyFrom;
import patterns.persistence.IPersistentObj;
import ptype.def.typedef.ITypeDef;

public interface IPTType extends IPersistentObj, ICopyFrom, IComparable {
    public static final int COMPARE_MAKES_NO_SENSE = -100;

    ITypeDef getTypeDef();

    boolean canRepresentAsType(IPTType fromValue);
    void setFromType(IPTType fromValue);

    boolean canRepresentAsBoolean();
    boolean getAsBoolean();
    void setFromBoolean(boolean setFrom);

    boolean canRepresentAsInteger();
    long getAsLong();
    void setFromLong(long setFrom);

    boolean canRepresentAsDouble();
    double getAsDouble();
    void setFromDouble(double setFrom);

    boolean canRepresentAsString();
    String getAsString();
    void setFromString(String setFrom);

    /**
     *
     * @param compTo Object to compare to
     * @return 0 if equal,
     * 1 if this Object is greater than compTo,
     * -1 it this Object is less than compTo
     * -100 (COMPARE_MAKES_NO_SENSE) if makes no sense to compare the objects
     */
    int typeCompareTo(IPTType compTo);

    boolean isValid();
}
