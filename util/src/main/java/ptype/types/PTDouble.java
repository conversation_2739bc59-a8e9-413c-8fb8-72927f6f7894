package ptype.types;

import buffer.IBaseBufferRO;
import buffer.IBaseBufferRW;
import buffer.helpers.bit.BitAddressableBuffer;
import buffer.helpers.bit.BitAddressableBufferCls;
import javaHelpers.ASSERT;
import ptype.IPTType;
import ptype.def.typedef.PDefDouble;

public class PTDouble extends PTType<PDefDouble> {
    private double value;

    public PTDouble(PDefDouble def) {
        super(def);
    }

    public PTDouble(PDefDouble def, double value) {
        super(def);
        this.value = value;
    }

    public PTDouble(double value) {
        super(PDefDouble.DefaultDef);
        this.value = value;
    }

    public void setValue(double value){
        this.value = value;
    }

    public double getValue(){
        return this.value;
    }

    @Override
    public boolean canRepresentAsType(IPTType fromValue) {
        return fromValue.canRepresentAsDouble();
    }

    @Override
    public void setFromType(IPTType fromValue) {
        ASSERT.IFTHROW(fromValue.canRepresentAsDouble(),"Can not set from type");
        this.value = fromValue.getAsDouble();
    }

    @Override
    public boolean canRepresentAsBoolean() {
        return true;
    }

    @Override
    public boolean getAsBoolean() {
        return this.value != 0;
    }

    @Override
    public void setFromBoolean(boolean setFrom) {
        if (setFrom){
            this.value = 1;
        }else{
            this.value = 0;
        }
    }

    @Override
    public boolean canRepresentAsInteger() {
        return true;
    }

    @Override
    public long getAsLong() {
        return Double.valueOf(this.value).longValue();
    }

    @Override
    public void setFromLong(long setFrom) {
        this.value = Long.valueOf(setFrom).doubleValue();
    }

    @Override
    public boolean canRepresentAsDouble() {
        return true;
    }

    @Override
    public double getAsDouble() {
        return this.value;
    }

    @Override
    public void setFromDouble(double setFrom) {
        this.value = setFrom;
    }

    @Override
    public boolean canRepresentAsString() {
        return true;
    }

    @Override
    public String getAsString() {
        return Double.toString(this.value);
    }

    @Override
    public void setFromString(String setFrom) {
        super.setFromString(setFrom);
    }

    @Override
    public long readObject(IBaseBufferRO buf, long bitPos) {
        this.value = BitAddressableBuffer.getDouble(buf,bitPos);
        return bitPos + getWriteSize();
    }

    @Override
    public long getWriteSize() {
        return BitAddressableBufferCls.getDoubleWriteSizeInBits();
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        return BitAddressableBuffer.setDouble(buf,bitPos,this.value);
    }
    @Override
    public int typeCompareTo(IPTType compTo) {
        if (compTo.canRepresentAsDouble()){
            double cmpTo = compTo.getAsDouble();
            if (value == cmpTo){
                return 0;
            }else if (value > cmpTo){
                return 1;
            }
            return 0;
        }
        ASSERT.THROW("can't compare to type");
        return 0;
    }
}