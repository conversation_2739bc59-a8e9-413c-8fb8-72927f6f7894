package ptype.types;

import buffer.IBaseBufferRO;
import buffer.IBaseBufferRW;
import buffer.helpers.bit.BitAddressableBuffer;
import javaHelpers.ASSERT;
import javaHelpers.NumberHelpers;
import ptype.IPTType;
import ptype.def.typedef.PDefInteger;

public class PTInteger extends PTType<PDefInteger> {

    private long value;

    public PTInteger(PDefInteger def) {
        super(def);
    }

    public PTInteger(PDefInteger def, long value) {
        super(def);
        setValue(value);
    }

    public PTInteger(long value) {
        super(PDefInteger.DefaultDef);
        setValue(value);
    }


    public void setValue(long value){
        ASSERT.IFTHROW((value & NumberHelpers.maskLS((byte)getWriteSize())) != 0,"value out of bounds");
        this.value = value;
    }

    public long getValue(){
        return this.value;
    }

    @Override
    public boolean canRepresentAsType(IPTType fromValue) {
        return fromValue.canRepresentAsInteger();
    }

    @Override
    public void setFromType(IPTType fromValue) {
        ASSERT.IFTHROW(!fromValue.canRepresentAsInteger(), "Can not be set from value");
        this.value = fromValue.getAsLong();
    }

    @Override
    public boolean canRepresentAsBoolean() {
        return true;
    }

    @Override
    public boolean getAsBoolean() {
        return this.value != 0;
    }

    @Override
    public void setFromBoolean(boolean setFrom) {
        if (setFrom){
            this.value = 1;
        }else{
            this.value = 0;
        }
    }

    @Override
    public boolean canRepresentAsInteger() {
        return true;
    }

    @Override
    public long getAsLong() {
        return this.value;
    }

    @Override
    public void setFromLong(long setFrom) {
        this.value = setFrom;
    }

    @Override
    public boolean canRepresentAsDouble() {
        return true;
    }

    @Override
    public double getAsDouble() {
        return Long.valueOf(this.value).doubleValue();
    }

    @Override
    public void setFromDouble(double setFrom) {
        this.value = Double.valueOf(setFrom).longValue();
    }

    @Override
    public boolean canRepresentAsString() {
        return true;
    }

    @Override
    public String getAsString() {
        return Long.valueOf(this.value).toString();
    }

    @Override
    public void setFromString(String setFrom) {
        this.value = Long.getLong(setFrom);
    }

    @Override
    public long getWriteSize() {
        return getTypeDef().getNumberBits();
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        return BitAddressableBuffer.setBitsAsLong(buf,bitPos,getWriteSize(),this.value);
    }


    @Override
    public long readObject(IBaseBufferRO buf, long bitPos) {
        this.value =  BitAddressableBuffer.getBitsAsLong(buf,bitPos,getWriteSize());
        this.value = NumberHelpers.signExtend(this.value, getTypeDef().getNumberBits());
        return bitPos + getWriteSize();
    }

    @Override
    public int typeCompareTo(IPTType compTo) {
        if (compTo.canRepresentAsInteger()){
            long cmpTo = compTo.getAsLong();
            if (getAsLong() == cmpTo){
                return 0;
            }else if (getAsLong() > cmpTo){
                return 1;
            }
            return -1;
        }
        ASSERT.THROW("can't compare to type");
        return 0;
    }

}
