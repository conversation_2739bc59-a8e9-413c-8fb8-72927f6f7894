package ptype.types;

import buffer.IBaseBufferRO;
import buffer.IBaseBufferRW;
import buffer.helpers.bit.BitAddressableBuffer;
import buffer.helpers.bit.BitAddressableBufferCls;
import buffer.types.PString;
import javaHelpers.ASSERT;
import ptype.IPTType;
import ptype.def.typedef.PDefString;

public class PTString extends PTType<PDefString> {
    private PString value;

    public PTString(PDefString def) {
        super(def);
        value = new PString();
    }

    public PTString(PDefString def, String value) {
        super(def);
        this.value = new PString(value, null);
    }

    public PTString(String value) {
        super(PDefString.DefaultDef);
        this.value = new PString(value, null);
    }


    public void setValue(PString value){
        this.value = value;
    }

    public PString getValue(){
        return this.value;
    }

    @Override
    public boolean canRepresentAsType(IPTType fromValue) {
        return fromValue.canRepresentAsString();
    }

    @Override
    public void setFromType(IPTType fromValue) {
        this.value = new PString (fromValue.getAsString(),null);
    }

    @Override
    public boolean canRepresentAsBoolean() {
        return getStringBuffer().getAsString().compareToIgnoreCase("true") == 0 || value.getAsString().compareToIgnoreCase("false") == 0 || canRepresentAsInteger();
    }

    @Override
    public boolean getAsBoolean() {
        if (getStringBuffer().getAsString().compareToIgnoreCase("true") == 0){
            return true;
        }else if (canRepresentAsInteger()) {
            long valueAsLong = getAsLong();
            return valueAsLong != 0;
        }else {
            return false;
        }
    }

    @Override
    public void setFromBoolean(boolean setFrom) {
        if (setFrom){
            getStringBuffer().setFromString("true");
        }else{
            getStringBuffer().setFromString("false");
        }
    }

    @Override
    public boolean canRepresentAsInteger() {
        String tmpVal = getStringBuffer().getAsString();
        if(tmpVal.matches("^[+-]?\\d+$"))
        {
            return true;
        }else return tmpVal.compareToIgnoreCase("true") == 0 || tmpVal.compareToIgnoreCase("false") == 0;
    }

    @Override
    public long getAsLong() {
        ASSERT.IFTHROW(!canRepresentAsInteger(),"String is not and integer");
        if (getStringBuffer().getAsString().compareToIgnoreCase("true") == 0 ){
            return 1;
        }else if (getStringBuffer().getAsString().compareToIgnoreCase("false") == 0){
            return 0;
        }
        return Long.parseLong(getStringBuffer().getAsString(),10);
    }

    @Override
    public void setFromLong(long setFrom) {
        getStringBuffer().setFromString(Long.toString(setFrom));
    }

    @Override
    public boolean canRepresentAsDouble() {
        if (getStringBuffer().getAsString().compareToIgnoreCase("true") == 0 || getStringBuffer().getAsString().compareToIgnoreCase("false") == 0){
            return true;
        }
        try{
            Double.parseDouble(getStringBuffer().getAsString());
        }
        catch (NumberFormatException ignored){
            return false;
        }
        return true;
    }

    @Override
    public double getAsDouble() {
        ASSERT.IFTHROW(!canRepresentAsDouble(),"String is not a double");
        if (getStringBuffer().getAsString().compareToIgnoreCase("true") == 0 ){
            return 1.0;
        }else if (getStringBuffer().getAsString().compareToIgnoreCase("false") == 0){
            return 0;
        }
        return Double.parseDouble(getStringBuffer().getAsString());
    }

    @Override
    public void setFromDouble(double setFrom) {
        getStringBuffer().setFromString(Double.toString(setFrom));
    }

    @Override
    public boolean canRepresentAsString() {
        return true;
    }

    @Override
    public String getAsString() {
        return getStringBuffer().getAsString();
    }

    @Override
    public void setFromString(String setFrom) {
        getStringBuffer().setFromString(setFrom);
    }

    @Override
    public long readObject(IBaseBufferRO buf, long bitPos) {
        int numBytes = BitAddressableBuffer.getInteger(buf, bitPos);
        bitPos += BitAddressableBufferCls.getIntWriteSizeInBits();

        this.value.clearAndSetBufSize(numBytes);
        BitAddressableBuffer.getString(buf,bitPos,numBytes,this.getStringBuffer());
        return bitPos + ((long) numBytes * Byte.SIZE);
    }

    @Override
    public long getWriteSize() {
        return ((long) this.getStringBuffer().getNumBytes() * Byte.SIZE) + BitAddressableBufferCls.getIntWriteSizeInBits();
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        bitPos = BitAddressableBuffer.setInteger(buf, bitPos,this.getStringBuffer().getNumBytes());
        return BitAddressableBuffer.setString(buf,bitPos,this.getStringBuffer());
    }

    private PString getStringBuffer(){
        if (this.value == null){
            this.value = new PString("",null);
        }

        return this.value;
    }

    @Override
    public int typeCompareTo(IPTType compTo) {
        if (compTo.canRepresentAsString()){
            String cmpTo = compTo.getAsString();
            return this.getAsString().compareTo(cmpTo);
        }
        ASSERT.THROW("can't compare to type");
        return 0;
    }
}
