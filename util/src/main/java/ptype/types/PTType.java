package ptype.types;

import javaHelpers.ASSERT;
import javaHelpers.ClassHelpers;
import patterns.copyObj.ICopyFrom;
import ptype.IPTType;
import ptype.def.typedef.ITypeDef;

public abstract class PTType<TYPE extends ITypeDef> implements IPTType {
    private final TYPE def;

    public PTType(TYPE def) {
        this.def = def;
    }

    @Override
    public TYPE getTypeDef() {
        return def;
    }

    @Override
    public boolean canRepresentAsType(IPTType fromValue) {
        // can always represent as type if same type
        return fromValue.getTypeDef() == getTypeDef();
    }

    @Override
    public void setFromType(IPTType fromValue) {
        ASSERT.THROW("Not implemented");
    }

    @Override
    public boolean canRepresentAsBoolean() {
        return false;
    }

    @Override
    public boolean getAsBoolean() {
        ASSERT.THROW("Not implemented");
        return false;
    }

    @Override
    public void setFromBoolean(boolean setFrom) {
        ASSERT.THROW("Not implemented");
    }

    @Override
    public boolean canRepresentAsInteger() {
        return false;
    }

    @Override
    public long getAsLong() {
        ASSERT.THROW("Not implemented");
        return 0;
    }

    @Override
    public void setFromLong(long setFrom) {
        ASSERT.THROW("Not implemented");

    }

    @Override
    public boolean canRepresentAsDouble() {
        return false;
    }

    @Override
    public double getAsDouble() {
        ASSERT.THROW("Not implemented");
        return 0;
    }

    @Override
    public void setFromDouble(double setFrom) {
        ASSERT.THROW("Not implemented");

    }

    @Override
    public boolean canRepresentAsString() {
        return false;
    }

    @Override
    public String getAsString() {
        ASSERT.THROW("Not implemented");
        return null;
    }

    @Override
    public void setFromString(String setFrom) {
        ASSERT.THROW("Not implemented");
    }

    @Override
    public Object cloneObj() {
        ICopyFrom copyTo = this.getTypeDef().createNewType();
        copyTo.copyFrom(this);
        return copyTo;
    }

    @Override
    public void copyFrom(ICopyFrom data) {
        IPTType type = ClassHelpers.castToClass(data);
        ASSERT.IFTHROW(!this.canRepresentAsType(type),"Can't copy from given type" );
        this.setFromType(type);

    }

    @Override
    public void copyTo(ICopyFrom data) {
        IPTType type = ClassHelpers.castToClass(data);
        ASSERT.IFTHROW(!type.canRepresentAsType(this),"Can't copy from given type" );
        type.setFromType(this);
    }

    @Override
    public int compareTo(Object compTo) {
        IPTType cls = ClassHelpers.getClassAsAssert(IPTType.class,compTo);
        return this.typeCompareTo(cls);

    }

    @Override
    public boolean isFixedSize(){
        return getTypeDef().isFixedSize();
    }

    @Override
    public boolean isValid(){
        return def.isValid(this);
    }

}
