package ptype.types;

import buffer.IBaseBufferRO;
import buffer.IBaseBufferRW;
import javaHelpers.ASSERT;
import javaHelpers.ClassHelpers;
import ptype.IPTType;
import ptype.def.typedef.PDefStruct;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class PTStruct extends PTType<PDefStruct> {
    private IPTType[] fields;
    private List<FieldEntry> optionalFields;

    public PTStruct(PDefStruct def) {
        super(def);
        this.fields = new IPTType[def.getNumberEntries()];
        this.optionalFields = new ArrayList<>();
    }

    public void setField(String name,IPTType field){
        int idx = getTypeDef().getIndex(name);
        if (idx < 0 ){
            this.optionalFields.add(new FieldEntry(name, field));
        }else{
            this.fields[idx] = field;
        }
    }

    public IPTType getField(String name){
        int idx = getTypeDef().getIndex(name);
        if (idx < 0 ){
            for(FieldEntry fieldEntry : this.optionalFields){
                if (fieldEntry.name.compareTo(name) == 0){
                    return fieldEntry.value;
                }
            }
        }else{
            return this.fields[idx];
        }
        return null;
    }

    public FieldEntry getField(int idx){
        if (idx<this.fields.length){
            return new FieldEntry(getTypeDef().getType(idx).name, this.fields[idx]);
        }else if (idx < this.fields.length + this.optionalFields.size()){
            return this.optionalFields.get(idx - this.fields.length);
        }
        return null;

    }

    private void clearFields(){
        Arrays.fill(this.fields, null);
        this.optionalFields.clear();
    }



    @Override
    public void setFromType(IPTType fromValue) {
        ASSERT.IFTHROW(!this.canRepresentAsType(fromValue),"not of correct type");
        PTStruct fromStruct = ClassHelpers.getClassAsAssert(PTStruct.class,fromValue);
        clearFields();

        int numberFromFields = fromStruct.getNumberFields();
        for(int i = 0;i < numberFromFields;++i){
            FieldEntry fromField = fromStruct.getField(i);
            setField(fromField.name, fromField.value);
        }

    }


    @Override
    public long readObject(IBaseBufferRO buf, long bitPos) {
        long readPos = bitPos;
        for(IPTType cur:this.fields){
            readPos = cur.readObject(buf,readPos);
        }
        return readPos;

    }

    @Override
    public long getWriteSize() {
        long writeSize = 0;
        for(IPTType cur:this.fields){
            writeSize += cur.getWriteSize();
        }
        return writeSize;

    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
       long writePos = bitPos;
        for(IPTType cur:this.fields){
            writePos = cur.writeObject(buf,writePos);
        }
        return writePos;
    }

    @Override
    public int typeCompareTo(IPTType compTo) {
        if (compTo.getTypeDef() != getTypeDef()){
            ASSERT.THROW("can't compare to type");
        }
        PTStruct compToInst = ClassHelpers.getClassAsAssert(PTStruct.class,compTo);

        PDefStruct thisType  = this.getTypeDef();
        PDefStruct compToType = ClassHelpers.getClassAsAssert(PDefStruct.class,compTo.getTypeDef());

        int numberFields = getNumberFields();
        for(int i = 0;i < numberFields;++i){
            FieldEntry thisField = this.getField(i);
            FieldEntry compToField = compToInst.getField(i);
            if (thisField == null && compToField == null){
                continue;
            }if (thisField == null){
                return -1;
            }if (compToField == null){
                return 1;
            }
            int cmpName = thisField.name.compareTo(compToField.name);
            if (cmpName != 0){
                return cmpName;
            }

            int cmp = thisField.value.typeCompareTo(compToField.value);
            if (cmp != 0){
                return cmp;
            }
        }

        if (numberFields < compToInst.getNumberFields()){
            return -1;
        }else if (numberFields > compToInst.getNumberFields()){
            return 1;
        }
        return 0;
    }


    public int getNumberFields() {
        return this.fields.length + this.optionalFields.size();
    }

    public static class FieldEntry{
        public String name;
        public IPTType value;

        public FieldEntry(String name, IPTType value) {
            this.name = name;
            this.value = value;
        }
    }
}
