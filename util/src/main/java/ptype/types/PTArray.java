package ptype.types;

import buffer.IBaseBufferRO;
import buffer.IBaseBufferRW;
import javaHelpers.ASSERT;
import ptype.IPTType;
import ptype.def.typedef.PDefArray;
import ptype.def.typedef.PDefStruct;

import java.util.List;

public class PTArray extends PTType<PDefArray>{
    List<IPTType> lstElements;

    public PTArray(PDefArray def) {
        super(def);
    }

    public void appendElement(IPTType element){
        this.lstElements.add(element);
    }

    @Override
    public int typeCompareTo(IPTType compTo) {
        if (compTo.getTypeDef() != getTypeDef()){
            ASSERT.THROW("can't compare to type");
        }
        return 0;
    }

    @Override
    public long readObject(IBaseBufferRO buf, long bitPos) {
        return 0;
    }

    @Override
    public long getWriteSize() {
        return 0;
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        return 0;
    }
}
