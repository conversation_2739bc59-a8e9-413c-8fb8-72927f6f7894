package ptype.def.builder;

import ptype.def.typedef.ITypeDef;
import ptype.def.typedef.PDefArray;
import ptype.types.PTArray;

import java.util.ArrayList;
import java.util.List;

public class PDefArrayBuilder extends PDefBuilder<PTArray> {
    private boolean openArray = false;
    private int minElements = PDefArray.MIN_UNSPECIFIED_MIN_ELEMENTS;
    private int maxElements = PDefArray.MAX_UNSPECIFIED_MAX_ELEMENTS;
    private List<ITypeDef<?>> allowedTypes;

    public PDefArrayBuilder() {
        this.allowedTypes = new ArrayList<>();
    }

    @Override
    public PDefArray Build() {
        ITypeDef<?>[] allowedTypesArray = allowedTypes.toArray(new ITypeDef<?>[0]);
        return new PDefArray(openArray, minElements, maxElements, allowedTypesArray);
    }

    /**
     * Sets whether this is an open array (can contain different types)
     * @param openArray true if the array can contain different types
     * @return this builder for method chaining
     */
    public PDefArrayBuilder setOpenArray(boolean openArray) {
        this.openArray = openArray;
        return this;
    }

    /**
     * Sets the minimum number of elements allowed in the array
     * @param minElements minimum number of elements, or MIN_UNSPECIFIED_MIN_ELEMENTS for no minimum
     * @return this builder for method chaining
     */
    public PDefArrayBuilder setMinElements(int minElements) {
        this.minElements = minElements;
        return this;
    }

    /**
     * Sets the maximum number of elements allowed in the array
     * @param maxElements maximum number of elements, or MAX_UNSPECIFIED_MAX_ELEMENTS for no maximum
     * @return this builder for method chaining
     */
    public PDefArrayBuilder setMaxElements(int maxElements) {
        this.maxElements = maxElements;
        return this;
    }

    /**
     * Adds an allowed type for elements in this array
     * @param allowedType the type definition that elements can have
     * @return this builder for method chaining
     */
    public PDefArrayBuilder addAllowedType(ITypeDef<?> allowedType) {
        this.allowedTypes.add(allowedType);
        return this;
    }

    /**
     * Clears all currently allowed types
     * @return this builder for method chaining
     */
    public PDefArrayBuilder clearAllowedTypes() {
        this.allowedTypes.clear();
        return this;
    }

    /**
     * Sets the allowed types, replacing any previously added types
     * @param allowedTypes array of type definitions that elements can have
     * @return this builder for method chaining
     */
    public PDefArrayBuilder setAllowedTypes(ITypeDef<?>... allowedTypes) {
        this.allowedTypes.clear();
        for (ITypeDef<?> type : allowedTypes) {
            this.allowedTypes.add(type);
        }
        return this;
    }
}
